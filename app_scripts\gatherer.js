// Google App Script to gather specific column data from source sheet to destination sheet
// Sheet Ids
const prodLogSheetId = "1wPIsn4Ggpfya6zvwF64iJRfo5-0GfgYpIew2YeUKRto";
const s1AGen2LogSheetId = "1G7kX96c7ItJ8Y89ZGsJUPOdtNu95uY75RQarXHLG4NE";
const moveOs4LogSheetId = "18Zuvs0wUxTfarLZrGHH5GH2LNgoeQuH1Hjao82LzdSA";
const s1XPlusSheetId = "1DANzkqr-Al-uAfPPRXGrk3OHwHMbSSvHibf_HYCKq-4"

// gathererSheet
const gathererSheetId = "1gHlGLQbQ27mymh3nXVybpHwXxrnm2Lb4z8_vT-VEP9A";
// dumpSheet
const dumbSheetId = "1zYB1jVJnPqLoptObFmkPyGt7ANw8Ko7hz2O4Z7L36dU"
// cloudDownloaderSheet
const cloudDownloaderSheetId = "1gjCw3wAv-6fUvsW-kQnql8-EMwJACK_Xj8DyQ9jF7ek"
// Active VIN List Sheet
ACTIVE_VIN_LIST_SHEET = "1bEA2XtFGZEXgCBjkDqWgjBohCat02AAj6rCq-3QMuGI"
VIN_ICCID_SHEET_NAME = "VIN_ICCID"
// Sheet Names
const destSheetNameAll = "ALL";
const sheetIdDict = {
    "OTHERS":s1AGen2LogSheetId,
    "PROD":prodLogSheetId,
    "MOVE_OS_4":moveOs4LogSheetId,
    "S1X_PLUS":s1XPlusSheetId,
}

const mapperObject = {
    'CONFIG': 'VEHICLE_CONFIG',
    'LOCATION': 'LOCATION',
    'VIN': 'VIN_NO',
    'BATTERY': 'BATTERY_DETAILS',
    'SOFTWARE': 'SOFTWARE',
    'SHIFT_DATE': 'SHIFT_DATE',
    'START_DATE': 'START_DATE',
    'END_DATE': 'END_DATE',
    'START_TIME': 'START_TIME',
    'END_TIME': 'END_TIME',
    'RIDER_TYPE': 'RIDER_TYPE',
    'DRIVE_CYCLE': 'DRIVE_CYCLE_ROUTE',
    'DISTANCE': 'TOTAL_KM',
    'DATA_LINK': 'RAW_DATA_LINK'
}

const sheetNamesOthers = ['RMZ S1_Air Log_Sheet',
                          'RMZ Gen_2 Log_Sheet',
                          'Ooty S1_Air Log_Sheet',
                          'Ooty Gen_2 Log_Sheet',
                          'Chennai S1_Air Log_Sheet',
                          'Chennai Gen_2 Log_Sheet',
                          'Jaisalmer S1_Air Log_Sheet',
                          'Monsoon S1_Air Log_Sheet',
                          'Monsoon Gen_2 Log_Sheet',
                          'Future_Factory Gen_2 Log_Sheet',
                          'Future_Factory S1_Air Log_Sheet',
                          'Yelagiri S1_AIR GEN_2 Log_Sheet',
                          'Leh Ladakh_S1_Air Log_Sheet',
                          'Leh Ladakh_Gen_2.0 Log_Sheet',
                          'FF-S1 Air PV Log Sheet',
                        ]

const sheetNamesProd = ['RMZ Running Log',
                        'FF_New S/W 3.0.3.gen2.0729 Log',
                        'BLR_New S/W 3.0.3.gen2.0729 Log',
                        'PV Vehicles_Chennai location',
                        ]

const sheetNamesMoveOs4 = ['Running Log',
                        ]

const sheetNamesS1XPlus = ['RMZ S1 X+',
                           'S1 X+ and S1 Air PV B2B_RMZ',
                           'Log Sheet_FF',
                           'Log Sheet FF - Phase 2'
                    ]

const locationObject = {
    "RMZ S1_Air Log_Sheet": "RMZ",
    "RMZ Gen_2 Log_Sheet": "RMZ",
    "Ooty S1_Air Log_Sheet": "OOTY",
    "Ooty Gen_2 Log_Sheet": "OOTY",
    "Chennai S1_Air Log_Sheet": "CHENNAI",
    "Chennai Gen_2 Log_Sheet": "CHENNAI",
    "Jaisalmer S1_Air Log_Sheet": "JAISALMER",
    "Monsoon S1_Air Log_Sheet": "MONSOON",
    "Monsoon Gen_2 Log_Sheet": "MONSOON",
    "Future_Factory Gen_2 Log_Sheet": "FF",
    "Future_Factory S1_Air Log_Sheet": "FF",
    "Yelagiri S1_AIR GEN_2 Log_Sheet": "YELAGIRI",
    'RMZ Running Log': "RMZ PROD",
    'FF_New S/W 3.0.3.gen2.0729 Log': 'FF PROD',
    'BLR_New S/W 3.0.3.gen2.0729 Log': "BLR PROD",
    'PV Vehicles_Chennai location': 'CHENNAI PROD',
    'Running Log': 'FF',
    'RMZ S1 X+': 'RMZ',
    'S1 X+ and S1 Air PV B2B_RMZ':'RMZ',
    'Log Sheet_FF':'FF',
    'FF-S1 Air PV Log Sheet':'FF PROD',
    'Leh Ladakh_Gen_2.0 Log_Sheet':'LEH',
    'Leh Ladakh_S1_Air Log_Sheet': 'LEH'
};

function getSheetDict() {
    return {"OTHERS": sheetNamesOthers, "PROD": sheetNamesProd, "MOVE_OS_4": sheetNamesMoveOs4, "S1X_PLUS": sheetNamesS1XPlus};
}

function sfVersionCleaner(sfVersion) {
    return String(sfVersion).replace(/[\n\s]/g, '');
}

// header for ALL
const getMapperHeaderList = () => {
    return Object.keys(mapperObject)
}
// Reverse Mapper Object
const mapperObjectReverse = {};
for (const key in mapperObject) {
    const value = mapperObject[key];
    mapperObjectReverse[value] = key;
}
// headers in Logs
const getMapperHeaderListReverse = () => {
    return Object.values(mapperObject)
}
// get neededList from mapperObject's values
const neededList = Object.values(mapperObject);

// Destination Spreadsheet
const destSpreadsheet = SpreadsheetApp.openById(gathererSheetId);

function getLinksFromSheet(sheet) {
    headerList = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    // store sheet name
    const sheetName = sheet.getName();
    // get range of links with column_name = "DATA_LINK" 
    linkHeaderName = headerList.includes(mapperObject["DATA_LINK"]) ? mapperObject["DATA_LINK"]:headerList.includes("DATA_LINK") ? "DATA_LINK" : null; 
    const dataLinkColumnIndex = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0].indexOf(linkHeaderName)
    const range = sheet.getRange(2, dataLinkColumnIndex + 1, sheet.getLastRow() - 1, 1)
    // range to string
    const rangeString = range.getA1Notation();
    // Get the RichTextValues for the range
    const richTextValues = sheet.getRange(rangeString).getRichTextValues();
    
    // Get a list of the links using getLinkUrl()
    const links =  richTextValues.map(row => {
        return row[0].getLinkUrl();
    });
    return links;
}

function loopThroughLogFilterBasedOnList(listOfDates) {
    var destSheet = destSpreadsheet.getSheetByName(destSheetNameAll);
    if (!destSheet) {
        destSheet = destSpreadsheet.insertSheet(destSheetNameAll);
    }
    var destHeaders = getMapperHeaderList();
    var batteryDict = {};
    // clear destSheet content
    destSheet.clear();
    destSheet.getRange(1, 1, 1, destHeaders.length).setValues([destHeaders]);
    destSheet.getRange(1, 1, 1, destHeaders.length).setFontWeight("bold").setHorizontalAlignment("center").setVerticalAlignment("middle").setWrapStrategy(SpreadsheetApp.WrapStrategy.WRAP).setFontSize(10).setBackground("#D9D9D9");
    const sheetDict = getSheetDict();
    Object.keys(sheetDict).forEach(category => {
        var sheetListCur = sheetDict[category];
        var sheetIdCur = sheetIdDict[category];
        // debugger
        sheetListCur.forEach(sheetName => {
            var sourceSheet = SpreadsheetApp.openById(sheetIdCur).getSheetByName(sheetName);
            // if sheet doesn't exist then skip
            if (!sourceSheet) {
                return;
            }
            var sourceHeaders = sourceSheet.getRange(1, 1, 1, sourceSheet.getLastColumn()).getValues()[0];
            // if there are headers from mapperObject.keys(), replace them with mapperObject.values()
            sourceHeaders = sourceHeaders.map(header => {
                if (header in mapperObject) {
                    return mapperObject[header];
                }
                return header;
            });
            var sourceData = sourceSheet.getRange(2, 1, sourceSheet.getLastRow() - 1, sourceSheet.getLastColumn()).getValues();
            var sourceLinksList = getLinksFromSheet(sourceSheet);
            // update batteryDict using columns ["VIN":"BATTERY"], if BATTERY is not present then skip
            var batteryIndex = sourceHeaders.indexOf(mapperObjectReverse["BATTERY"]);
            if (batteryIndex != -1) {
                sourceData.forEach(row => {
                    batteryDict[row[0]] = row[batteryIndex];
                });
            }
            var sourceDataMapped = sourceData.map(row => {
                const emptyRow = {};
                for (const [index, col] of row.entries()) {
                    const sourceHeader = sourceHeaders[index];
                    emptyRow[sourceHeader] = col;
                }
                return emptyRow;
            });
            sourceDataMapped.forEach((row, index) => {
                row["DATA_LINK"] = sourceLinksList[mapperObject["DATA_LINK"]];
            });
            var sourceDataNeeded = sourceDataMapped.map(row => {
                const emptyRow = {};
                try{
                  for (const [index, col] of Object.entries(row)) {
                      if (neededList.includes(index)) {
                          // if index in ['SHIFT_DATE', 'START_DATE', 'END_DATE'] then convert to date format YYYY-MM-DD
                          if (mapperObjectReverse[index] == 'SHIFT_DATE' || mapperObjectReverse[index] == 'START_DATE' || mapperObjectReverse[index] == 'END_DATE') {
                              emptyRow[mapperObjectReverse[index]] = Utilities.formatDate(new Date(col), "GMT+5:30", "yyyy-MM-dd");
                          } 
                          // if index in ['START_TIME', 'END_TIME'] then convert to time format HH:MM:SS
                          else if (mapperObjectReverse[index] == 'START_TIME' || mapperObjectReverse[index] == 'END_TIME') {
                              // if col is empty then skip
                              if (col == "") {
                                  continue;
                              }
                              emptyRow[mapperObjectReverse[index]] = Utilities.formatDate(new Date(col), "GMT+5:30", "HH:mm:ss");
                          }
                          else if(mapperObjectReverse[index] == "CONFIG"){
                            // remove all special characters from col
                            // parseString(col);
                            col_copy = col.toString();
                            col_copy = col_copy.replace(/[^a-zA-Z0-9+]/g, "");
                            col_copy = col_copy.toUpperCase();
                            emptyRow[mapperObjectReverse[index]]= getConfigDetails(col_copy, category);

                          }
                          else if(mapperObjectReverse[index] == "RIDER_TYPE"){
                              colStr = col.toString();
                              riderType = colStr.toUpperCase();
                              // remove special characters from riderType
                              riderType = riderType.replace(/[^a-zA-Z0-9]/g, "");
                              emptyRow[mapperObjectReverse[index]]= getRiderTypeAcronym(riderType);
                          }else if(mapperObjectReverse[index] == "DRIVE_CYCLE"){
                              colStr = col.toString();
                              driveCycle = colStr.toUpperCase();
                              driveCycle = driveCycle.replace(/[^a-zA-Z0-9]/g, "");
                              emptyRow[mapperObjectReverse[index]]= getDriveCycleAcrnym(driveCycle, col);
                          }else if (mapperObjectReverse[index] == "BATTERY") {
                              if (col == "") {
                                  // if VIN in batteryDict then BATTERY = batteryDict[VIN]
                                  emptyRow[mapperObjectReverse[index]] = (emptyRow[mapperObjectReverse["VIN"]] in batteryDict) ? batteryDict[emptyRow[mapperObjectReverse["VIN"]]].replace(/-\*/g, "").toUpperCase() : "XX";
                              }
                              else {
                                  var colStr = col.toString(); 
                                  if(colStr.includes("-")){
                                      emptyRow[mapperObjectReverse[index]] = col.split("-")[0].toUpperCase();
                                  }else{
                                      emptyRow[mapperObjectReverse[index]] = colStr.toUpperCase();
                                  }
                              }
                          }else if(mapperObjectReverse[index] == "SOFTWARE"){
                              // if empty then SOFTWARE = "XX"
                              if(col == ""){
                                  emptyRow[mapperObjectReverse[index]] = "XX";
                              }else{
                                  emptyRow[mapperObjectReverse[index]] = sfVersionCleaner(col);
                              }
                          }else {
                              emptyRow[mapperObjectReverse[index]] = col;
                          }
                        }
                      }
                      emptyRow['LOCATION'] = locationObject[sheetName];
                      return emptyRow;
                    } catch(e){
                      console.log(e);
                    }
                  });
            sourceDataNeeded = sourceDataNeeded.filter(row => {
                var rowDate = new Date(row["SHIFT_DATE"]);
                return listOfDates.includes(Utilities.formatDate(rowDate, "GMT+5:30", "yyyy-MM-dd"));
            }); 

            // Destination sheet mapper
            var destData = [];
            sourceDataNeeded.forEach(row => {
                var destRow = [];
                // debugger
                Object.keys(mapperObject).forEach(key => {
                    // debugger
                    if (key in row) {
                        destRow.push(row[key]);
                    } else {
                        destRow.push("");
                    }
                });
                destData.push(destRow);
            });
            // if destData is empty then return
            if (destData.length == 0) {
                return;
            }
            destSheet.getRange(destSheet.getLastRow() + 1, 1, destData.length, destData[0].length).setValues(destData);
        });
    });

}

// Appender Choice Function
function appenderChoice() {
    // As user if they want to Append Prod 24h sheet
    const appendProd24h = Browser.msgBox("Append Prod 24h sheet?", Browser.Buttons.YES_NO);
    // alert value of appendProd24h
    Browser.msgBox(appendProd24h);
    if(appendProd24h == "yes"){
        cloudSplitAppender("24h");
    }else{
        cloudSplitAppender();
    }
}

function headerSetterFormatter(sheet, headers){
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight("bold").setHorizontalAlignment("center").setVerticalAlignment("middle").setBackground("#D9D9D9");
}
// cloudSplitAppender function: Same functionality as cloudSplit() but will append data to existing sheets
function cloudSplitAppender(sourceSheetName = "24h", appendFlag = false) {
    // sheet names in downloader
    const cloudSheets = returnListOfDownloaderSheets();
    const count = cloudSheets.length;
    // read ALL sheet from destSpreadsheet
    const allSheet = destSpreadsheet.getSheetByName(sourceSheetName);
    const allHeaders = allSheet.getRange(1, 1, 1, allSheet.getLastColumn()).getValues()[0];
    // read data
    const allData = allSheet.getRange(2, 1, allSheet.getLastRow() - 1, allSheet.getLastColumn()).getValues();
    // shuffle data in random order
    allData.sort(() => Math.random() - 0.5);
    // column indices of "START_DATE" and "END_DATE"
    const startDateIndex = allHeaders.indexOf("START_DATE");
    const endDateIndex = allHeaders.indexOf("END_DATE");
    const startTimeIndex = allHeaders.indexOf("START_TIME");
    const endTimeIndex = allHeaders.indexOf("END_TIME");
    // Cleaning Before Splitting
    for (var i = 0; i < allData.length; i++) {
        // if START_DATE, END_DATE, START_TIME, END_TIME empty then remove row
        if (allData[i][startDateIndex] == "" || allData[i][endDateIndex] == "" || allData[i][startTimeIndex] == "" || allData[i][endTimeIndex] == "") {
            allData.splice(i, 1);
            i--;
        }
        // if VIN,CONFIG empty then remove row
        if(allData[i][allHeaders.indexOf("VIN")] == "" || allData[i][allHeaders.indexOf("CONFIG")] == ""){
            allData.splice(i, 1);
            i--;
        }
        // if CONFIG contains "BENCH" then remove row
        if(allData[i][allHeaders.indexOf("CONFIG")].includes("BENCH")){
            allData.splice(i, 1);
            i--;
        }
    }
    // Split data into count sheets equally based on count
    var cloudDataSplit = [];
    for(var i = 0; i < count; i++) {
        cloudDataSplit.push([]);
    }
    // split data into 6 arrays
    for(var i = 0; i < allData.length; i++) {
        cloudDataSplit[i % count].push(allData[i]);
    }
    // from cloudDownloaderSheetId
    const cloudDownloadSheet = SpreadsheetApp.openById(cloudDownloaderSheetId);
    cloudSheets.forEach(sheetName => {
        if (!cloudDownloadSheet.getSheetByName(sheetName)) {
            cloudDownloadSheet.insertSheet(sheetName);
        }
        headerSetterFormatter(cloudDownloadSheet.getSheetByName(sheetName), allHeaders);
        if(!appendFlag){
            // try to clear sheet from second row
            try{
                cloudDownloadSheet.getSheetByName(sheetName).getRange(2, 1, cloudDownloadSheet.getSheetByName(sheetName).getLastRow() - 1, cloudDownloadSheet.getSheetByName(sheetName).getLastColumn()).clearContent();
            }catch(e){
                console.log(e);
            }
        }
        cloudDownloadSheet.getSheetByName(sheetName).getRange(cloudDownloadSheet.getSheetByName(sheetName).getLastRow() + 1, 1, cloudDataSplit[cloudSheets.indexOf(sheetName)].length, cloudDataSplit[cloudSheets.indexOf(sheetName)][0].length).setValues(cloudDataSplit[cloudSheets.indexOf(sheetName)]);
        // format SHIFT_DATE	START_DATE	END_DATE as "YYYY-MM-DD" format
        cloudDownloadSheet.getSheetByName(sheetName).getRange(2, allHeaders.indexOf("SHIFT_DATE") + 1, cloudDownloadSheet.getSheetByName(sheetName).getLastRow() - 1, 3).setNumberFormat("yyyy-mm-dd");
        // format START_TIME	END_TIME as "HH:MM:SS" format
        cloudDownloadSheet.getSheetByName(sheetName).getRange(2, allHeaders.indexOf("START_TIME") + 1, cloudDownloadSheet.getSheetByName(sheetName).getLastRow() - 1, 2).setNumberFormat("hh:mm:ss");
        // format sheet as Plain Text
        cloudDownloadSheet.getSheetByName(sheetName).getRange(2, 1, cloudDownloadSheet.getSheetByName(sheetName).getLastRow() - 1, cloudDownloadSheet.getSheetByName(sheetName).getLastColumn()).setNumberFormat("@");
    })
}

function clearAllSixSheetsRetainHeaders(){
    // from cloudDownloaderSheetId
    const cloudDownloadSheet = SpreadsheetApp.openById(cloudDownloaderSheetId);
    // create 6 sheets if they don't exist
    const cloudSheets = returnListOfDownloaderSheets();
    cloudSheets.forEach(sheetName => {
        if (!cloudDownloadSheet.getSheetByName(sheetName)) {
            cloudDownloadSheet.insertSheet(sheetName);
        }
        // clear sheet
        cloudDownloadSheet.getSheetByName(sheetName).clear();
        // paste headers
        cloudDownloadSheet.getSheetByName(sheetName).getRange(1, 1, 1, 14).setValues([["CONFIG", "LOCATION", "VIN", "BATTERY", "SOFTWARE", "SHIFT_DATE", "START_DATE", "END_DATE", "START_TIME", "END_TIME", "RIDER_TYPE", "DRIVE_CYCLE", "DISTANCE", "Processed"]]);
        // format header
        cloudDownloadSheet.getSheetByName(sheetName).getRange(1, 1, 1, 14).setFontWeight("bold").setHorizontalAlignment("center").setVerticalAlignment("middle").setBackground("#D9D9D9");
    });
}

function appSheetToALLSheet(){
    // appsheet_vehicle_info sheet to ALL sheet
    // ALL sheet headers : CONFIG	LOCATION	VIN	BATTERY	SOFTWARE	SHIFT_DATE	START_DATE	END_DATE	START_TIME	END_TIME	RIDER_TYPE	DRIVE_CYCLE	DISTANCE	DATA_LINK
    const allSheet = destSpreadsheet.getSheetByName("ALL");
    const appsheetSheet = destSpreadsheet.getSheetByName("appsheet_vehicle_info");

    // Get headers from ALL sheet
    const allHeaders = allSheet.getRange(1, 1, 1, allSheet.getLastColumn()).getValues()[0];
    const appsheetHeaders = appsheetSheet.getRange(1, 1, 1, appsheetSheet.getLastColumn()).getValues()[0];

    // Read data from appsheet_vehicle_info sheet
    const appsheetData = appsheetSheet.getRange(2, 1, appsheetSheet.getLastRow() - 1, appsheetSheet.getLastColumn()).getValues();

    // Create a mapping from appsheet headers to ALL headers
    const headerMapping = {
        'VIN': 'VIN',
        'CONFIG': 'Model',
        'LOCATION': 'Location',
        'BATTERY': 'Battery',
        'SOFTWARE': 'Status',
        'SHIFT_DATE': 'Updated Date',
        'START_DATE': 'Received Date',
        'END_DATE': 'Updated Date',
        'START_TIME': 'Created Date',
        'END_TIME': 'Updated Date',
        'DISTANCE': 'Total Rides',
        'DATA_LINK': 'Related Vehicle History'
    };
    // Prepare data to be written to ALL sheet
    const allData = appsheetData.map(row => {
        return allHeaders.map(header => {
            const appsheetHeader = headerMapping[header];
            const value = appsheetHeader ? row[appsheetHeaders.indexOf(appsheetHeader)] : '';
            return value || 'XX'; // Use '-' as placeholder for blank values
        });
    });
    // Clear ALL sheet
    allSheet.clear();
    // Write headers to ALL sheet
    allSheet.getRange(1, 1, 1, allHeaders.length).setValues([allHeaders]);
    allSheet.getRange(1, 1, 1, allHeaders.length).setFontWeight("bold").setHorizontalAlignment("center").setVerticalAlignment("middle").setBackground("#D9D9D9");
    // Write data to ALL sheet
    allSheet.getRange(2, 1, allData.length, allData[0].length).setValues(allData);

}


// function to get unique porduction vins from "ALL" sheet
function get24hrAllVINsFromAllSheet() {
    // shift_date is decided based on current time, if time is between 00:00:00 and 07:59:59 then date is previous day else date is current day
    const currentDate = new Date();
    const currentHour = currentDate.getHours();
    const currentMinute = currentDate.getMinutes();
    const currentSecond = currentDate.getSeconds();
    const currentDay = currentDate.getDate();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    const shiftDateNeeded = new Date(currentYear, currentMonth, currentDay, currentHour, currentMinute, currentSecond);
    // current date in "YYYY-MM-DD" format
    const currentDateNeededString = Utilities.formatDate(shiftDateNeeded, "GMT+5:30", "yyyy-MM-dd");
    // yesterday date in "YYYY-MM-DD" format using shiftDateNeeded
    const yesterdayDateNeededString = Utilities.formatDate(new Date(shiftDateNeeded.getTime() - 24 * 60 * 60 * 1000), "GMT+5:30", "yyyy-MM-dd");
    const dateDayBeforeYesterdayString = Utilities.formatDate(new Date(shiftDateNeeded.getTime() - 2 * 24 * 60 * 60 * 1000), "GMT+5:30", "yyyy-MM-dd");
    // read ALL sheet from destSpreadsheet
    const allSheet = destSpreadsheet.getSheetByName("ALL");
    // Sync All Sheet with VIN_ICCID
    syncVinIccidToAllSheet()
    const resultSheet = destSpreadsheet.getSheetByName("24h") ? destSpreadsheet.getSheetByName("24h") : destSpreadsheet.insertSheet("24h");
    const allHeaders = allSheet.getRange(1, 1, 1, allSheet.getLastColumn()).getValues()[0];
    // read data
    const allData = allSheet.getRange(2, 1, allSheet.getLastRow() - 1, allSheet.getLastColumn()).getValues();
    // shiftDate
    const shiftDate = new Date(allData[0][allHeaders.indexOf("SHIFT_DATE")]);
    const shiftDateStr = Utilities.formatDate(shiftDate, "GMT+5:30", "yyyy-MM-dd");

    const uniqueProductionVins = [];
    // keep only unique production vins and remove all rows
    for(var i = 0; i < allData.length; i++) {
        if(!uniqueProductionVins.includes(allData[i][allHeaders.indexOf("VIN")])){
            uniqueProductionVins.push(allData[i][allHeaders.indexOf("VIN")]);
        } else {
            allData.splice(i, 1);
            i--;
        }
    }
    for(var i = 0; i < allData.length; i++) {
        // shift_date as "24h "+shiftDateStr
        allData[i][allHeaders.indexOf("SHIFT_DATE")] = "24h " + yesterdayDateNeededString;
        allData[i][allHeaders.indexOf("START_DATE")] = dateDayBeforeYesterdayString
        allData[i][allHeaders.indexOf("END_DATE")] = yesterdayDateNeededString
        allData[i][allHeaders.indexOf("START_TIME")] = "23:59:59";
        allData[i][allHeaders.indexOf("END_TIME")] = "23:59:59";
    }
    // clear result sheet
    resultSheet.clear();
    // write data to resultSheet
    resultSheet.getRange(1, 1, 1, allHeaders.length).setValues([allHeaders]);
    resultSheet.getRange(1, 1, 1, allHeaders.length).setFontWeight("bold").setHorizontalAlignment("center").setVerticalAlignment("middle").setBackground("#D9D9D9");
    
    // write all data to resultSheet
    resultSheet.getRange(2, 1, allData.length, allData[0].length).setValues(allData);
    // format
    resultSheet.getRange(2, 1, allData.length, allData[0].length).setValues(allData);
    // format SHIFT_DATE	START_DATE	END_DATE as "YYYY-MM-DD" format
    resultSheet.getRange(2, allHeaders.indexOf("SHIFT_DATE") + 1, allData.length, 3).setNumberFormat("yyyy-mm-dd");
    // format START_TIME	END_TIME as "HH:MM:SS" format
    resultSheet.getRange(2, allHeaders.indexOf("START_TIME") + 1, allData.length, 2).setNumberFormat("hh:mm:ss");
    clearAllSixSheetsRetainHeaders();
    // Appender
    cloudSplitAppender("24h");
}

// function to get unique porduction vins from "ALL" sheet
function get24hrAllVINsFromAllSheetInputDate() {
    // shift_date is decided based on current time, if time is between 00:00:00 and 07:59:59 then date is previous day else date is current day
    const currentDate = new Date();
    const currentDay =  parseInt(Browser.inputBox("Enter Day", "Enter Day", Browser.Buttons.OK_CANCEL));
    const currentMonth =  parseInt(Browser.inputBox("Enter Month", "Enter Month", Browser.Buttons.OK_CANCEL));
    const currentYear =  parseInt(Browser.inputBox("Enter Year", "Enter Year", Browser.Buttons.OK_CANCEL));
    const shiftDateNeeded = new Date(currentYear, currentMonth-1, currentDay+1, 0, 0, 0);
    // current date in "YYYY-MM-DD" format
    const currentDateNeededString = Utilities.formatDate(shiftDateNeeded, "GMT+5:30", "yyyy-MM-dd");
    // debugger
    // yesterday date in "YYYY-MM-DD" format using shiftDateNeeded
    const yesterdayDateNeededString = Utilities.formatDate(new Date(shiftDateNeeded.getTime() - 24 * 60 * 60 * 1000), "GMT+5:30", "yyyy-MM-dd");
    const dateDayBeforeYesterdayString = Utilities.formatDate(new Date(shiftDateNeeded.getTime() - 2 * 24 * 60 * 60 * 1000), "GMT+5:30", "yyyy-MM-dd");
    // read ALL sheet from destSpreadsheet
    const allSheet = destSpreadsheet.getSheetByName("ALL");
    // clear ALL sheet
    allSheet.clear()
    loopThroughLogFilterBasedOnList([dateDayBeforeYesterdayString, yesterdayDateNeededString]);
    // loopThroughSheetsGetInfoAll(dateDayBeforeYesterdayString);
    // loopThroughSheetsGetInfoAll(yesterdayDateNeededString);
    // result sheet "PROD 24h", create if it doesn't exist
    const resultSheet = destSpreadsheet.getSheetByName("24h") ? destSpreadsheet.getSheetByName("24h") : destSpreadsheet.insertSheet("24h");
    const allHeaders = allSheet.getRange(1, 1, 1, allSheet.getLastColumn()).getValues()[0];
    // read data
    const allData = allSheet.getRange(2, 1, allSheet.getLastRow() - 1, allSheet.getLastColumn()).getValues();
    // shiftDate
    const shiftDate = new Date(allData[0][allHeaders.indexOf("SHIFT_DATE")]);
    const shiftDateStr = Utilities.formatDate(shiftDate, "GMT+5:30", "yyyy-MM-dd");

    const uniqueProductionVins = [];
    // keep only unique production vins and remove all rows
    for(var i = 0; i < allData.length; i++) {
        if(!uniqueProductionVins.includes(allData[i][allHeaders.indexOf("VIN")])){
            uniqueProductionVins.push(allData[i][allHeaders.indexOf("VIN")]);
        } else {
            allData.splice(i, 1);
            i--;
        }
    }
    for(var i = 0; i < allData.length; i++) {
        // shift_date as "24h "+shiftDateStr
        allData[i][allHeaders.indexOf("SHIFT_DATE")] = "24h " + yesterdayDateNeededString;
        allData[i][allHeaders.indexOf("START_DATE")] = dateDayBeforeYesterdayString
        allData[i][allHeaders.indexOf("END_DATE")] = yesterdayDateNeededString
        allData[i][allHeaders.indexOf("START_TIME")] = "23:59:59";
        allData[i][allHeaders.indexOf("END_TIME")] = "23:59:59";
    }
    // clear result sheet
    resultSheet.clear();
    // write data to resultSheet
    resultSheet.getRange(1, 1, 1, allHeaders.length).setValues([allHeaders]);
    resultSheet.getRange(1, 1, 1, allHeaders.length).setFontWeight("bold").setHorizontalAlignment("center").setVerticalAlignment("middle").setBackground("#D9D9D9");
    
    // write all data to resultSheet
    resultSheet.getRange(2, 1, allData.length, allData[0].length).setValues(allData);
    // format
    resultSheet.getRange(2, 1, allData.length, allData[0].length).setValues(allData);
    // format SHIFT_DATE	START_DATE	END_DATE as "YYYY-MM-DD" format
    resultSheet.getRange(2, allHeaders.indexOf("SHIFT_DATE") + 1, allData.length, 3).setNumberFormat("yyyy-mm-dd");
    // format START_TIME	END_TIME as "HH:MM:SS" format
    resultSheet.getRange(2, allHeaders.indexOf("START_TIME") + 1, allData.length, 2).setNumberFormat("hh:mm:ss");
    clearAllSixSheetsRetainHeaders();
    // Appender
    cloudSplitAppender("24h");
}

const getDriveCycleAcrnym = (driveCycle, col) => {
    if((driveCycle.includes("CITY") && driveCycle.includes("HIGHWAY")) || (driveCycle.includes("CITY") && driveCycle.includes("HW")) || (driveCycle.includes("C") && driveCycle.includes("HW"))){
        return "CHW";
    }else if(driveCycle.includes("HIGHWAY")){
        return "HW";
    }else if(driveCycle.includes("CITY")){
        return "C";
    }else if(driveCycle.includes("ROUGHROAD") || driveCycle.includes("ROUGH") || driveCycle.includes("RR")){
        return "RR";
    }else if(driveCycle.includes("HILL")){
        return "HD";
    }else if((driveCycle.includes("OUS") && driveCycle.includes("HYP"))){
        return "CH";
    } else if(driveCycle.includes("HYP")){
        return "H";
    } else if (driveCycle.includes("LB") || driveCycle.includes('UB')) {
        return "LB UB";
    } else if (driveCycle.includes("YELAGIRI") || driveCycle.includes('YELAGIRI ')) {
        return "HD";
    }
    else{
        colStr = col.toString();
        return colStr.toUpperCase();
    }
}

const getRiderTypeAcronym = (riderType) => {
    if(riderType.includes("RIDER") && riderType.includes("PILLION") && riderType.includes("SANDBANG")){
        return "RPS";
    }else if(riderType.includes("RIDER") && riderType.includes("PILLION")){
        return "RP";
    }else if(riderType.includes("RIDER") && riderType.includes("SANDBANG")){
        return "RS";
    }else if(riderType.includes("RIDER")){
        return "R";
    }else{
        return "R";
    }
}

const getConfigDetails = (col_copy, category) => {
    if (col_copy.includes("GEN2") && col_copy.includes("AIR")) {
        return category == "PROD" ? "S1 AIR GEN2 PROD" : "S1 AIR GEN2";
    }
    if (col_copy.includes("GEN2") && col_copy.includes("PRO")) {
        return category == "PROD" ? "S1 PRO GEN2 PROD" : "S1 PRO GEN2";
    }
    if (col_copy.includes("AIR")) {
        return (category == "PROD" || col_copy.includes("PV")) ? "S1 AIR PROD" : "S1 AIR";
    }
    if (col_copy.includes("GEN2")) {
        return category == "PROD" || col_copy.includes("PV") ? "GEN2 PROD" : col_copy.includes("DV") ? "GEN2 DEV" : "GEN2";
    }
    if (col_copy.includes("PRO")) {
        return col_copy.includes("PV") ? "S1 PRO PROD" : "S1 PRO";
    }
    if ((col_copy.includes("X") && col_copy.includes("PLUS")) || (col_copy.includes("X") && col_copy.includes("+")) ) {
        return col_copy.includes('PV') ? "S1 X PLUS PROD" : col_copy.includes('PROD') ? "S1 X PLUS PROD" : "S1 X PLUS";
    }
    if (col_copy.includes('X') && col_copy.includes('S1')) {
        return col_copy.includes('PV') ? "S1 X PROD" : "S1 X";
    }
    if (col_copy.includes('RR')) {
        return col_copy.includes('PV') || col_copy.includes('PROD') ? "S1 RR PROD" : "S1 RR";
    }
    return category == "PROD" ? col_copy.toUpperCase() + " PROD" : col_copy.toUpperCase();
}

// sheet in downloader list
function returnListOfDownloaderSheets(){
    return [
        "ARIF_CLOUD", 
        "SACHIN_CLOUD", 
        "JANA_CLOUD", 
        "SHARAN_CLOUD", 
        "GARIMA_CLOUD", 
        // "NIKHIL_CLOUD"
    ];
}
