library(DBI)
library(RPresto)
library(yaml)
library(olaprestoconnector)
library(googledrive)
library(googlesheets4)
library(data.table)
library(jsonlite)
# drive upload library
library(purrr)
library(future)
# Connect to Presto
conn1 <- prestoConnect("hive-az", "default")
drive_conn <- drive_auth(email = "<EMAIL>")
gs4_auth(token = drive_token())

# Set the ID of the Google Sheet to get the download information
sheet_id <- "1gjCw3wAv-6fUvsW-kQnql8-EMwJACK_Xj8DyQ9jF7ek"
gatherer_sheet_id <- "1gHlGLQbQ27mymh3nXVybpHwXxrnm2Lb4z8_vT-VEP9A"
worksheet_name <- "ARIF_CLOUD"
# user_name from worksheet_name.split('_')[0]
user_name <- strsplit(worksheet_name, "_")[[1]][1]

# Google Drive Related Information
cloud_path = "VETV_SOFTWARE_TEAM/CLOUD"
cloud_folder <- drive_get(path = cloud_path)
cloud_folder_id <- as_id(cloud_folder)
charging_data_folder_id <- "1CtkH9Vjgz70kAehaR1OPXJSVN0zs2H8-"

# Download Folder Setup - Regular Cloud All
output_directory <- "output-sql-24h"
# create folder if not exists
if (!dir.exists(output_directory)) {
  dir.create(output_directory)
}
# Charging Data
charging_output_directory <- "charging-data-24h"
if (!dir.exists(charging_output_directory)) {
  dir.create(charging_output_directory)
}

# Define the retry_gs_operation function
retry_gs_operation <- function(operation_function) {
  max_retries <- 6
  delay_seconds <- 30
  max_delay_seconds <- 60
  result <- NULL
  
  for (retry_count in 1:max_retries) {
    try_result <- tryCatch({
      cat("Attempting Google Sheets operation (attempt", retry_count, ")\n")
      result <- operation_function()
      return(result)  # Return the result if successful
    }, error = function(err) {
      return(err)
    })
    
    if (!inherits(try_result, "error")) {
      cat("Google Sheets operation succeeded on attempt", retry_count, "\n")
      return(result)  # Return the result
    } else {
      cat("Google Sheets operation failed on attempt", retry_count, "\n")
    }
    
    if (retry_count < max_retries) {
      cat("Waiting", delay_seconds, "seconds before retrying\n")
      Sys.sleep(delay_seconds)
      delay_seconds <- min(delay_seconds * 2, max_delay_seconds)
    } else {
      cat("Max retry attempts reached. Google Sheets operation failed.\n")
      stop("Max retry attempts reached.")
    }
  }
}

# Function to perform the read_sheet operation
read_sheet_operation <- function() {
  return(as.data.frame(read_sheet(sheet_id, range = paste0(worksheet_name, "!A:M"))))
}

# dump sheet
dump_sheet_id <- "1zYB1jVJnPqLoptObFmkPyGt7ANw8Ko7hz2O4Z7L36dU"
dump_worksheet_name <- "TRACKER"
# Read the list of vehicle identification numbers and dates from the input worksheet
# input_data <- as.data.frame(read_sheet(sheet_id, range = "ARIF_CLOUD!A:M"))
# Use the retry_gs_operation function to handle the read_sheet operation
input_data <- retry_gs_operation(read_sheet_operation)
input_matrix <- as.matrix(input_data)
# if data is empty then exit
vetv_driver_id <- "1Sb6M67z0B-ytQuArqcRkBL1bklhK7AmL"

# Get values frm
vehicle_col <- which(colnames(input_matrix) == "VIN")
# r_date_col <- which(colnames(input_matrix) == "r_date")
shift_date_col <- which(colnames(input_matrix) == "SHIFT_DATE")
start_date_col <- which(colnames(input_matrix) == "START_DATE")
start_time_col <- which(colnames(input_matrix) == "START_TIME")
end_date_col <- which(colnames(input_matrix) == "END_DATE")
end_time_col <- which(colnames(input_matrix) == "END_TIME")
location_col <- which(colnames(input_matrix) == "LOCATION")
model_col <- which(colnames(input_matrix) == "CONFIG")
software_col <- which(colnames(input_matrix) == "SOFTWARE")
battery_col <- which(colnames(input_matrix) == "BATTERY")
rider_type_col <- which(colnames(input_matrix) == "RIDER_TYPE")
drive_cycle_col <- which(colnames(input_matrix) == "DRIVE_CYCLE") 

# function with one argument, folder_name within cloud_folder_id. function checks if folder_name is duplicated and merges them to a single file
folder_merger <- function(folder_id_list) {
  keep_id <- folder_id_list[1]
  for (i in 2:length(folder_id_list)) {
    file_list <- drive_ls(path = as_id(folder_id_list[i]))
    for (j in 1:nrow(file_list)) {
      drive_mv(file = as_id(file_list$id[j]), path = as_id(keep_id))
    }
    drive_rm(as_id(folder_id_list[i]))
  }
  return(keep_id)
}
# drive_mkdir(path = cloud_folder_id, name=shift_date)
folder_creator <- function(path, name) {
  shift_date_folder <- drive_mkdir(path = path, name=name)
  return(as_id(shift_date_folder))
}
# Loop through the unique input rows and run the query for each row that has not yet been processed
folder_flag <- FALSE
if (nrow(input_matrix) > 0) {
  for (i in 1:nrow(input_matrix)) {
    flag <- FALSE
    drive_operations_successful <- TRUE 
    sql_operations_successful <- TRUE
    # Read the values from the current row
    vehicle_identification_number <- input_matrix[i, vehicle_col]
    # r_date <- as.character(input_matrix[i, r_date_col])
    start_date <- as.character(input_matrix[i, start_date_col])
    # r_date is start_date - 1 day, in "YYYY-MM-DD" format
    r_date <- as.character(as.Date(start_date) - 1)
    start_time <- as.character(input_matrix[i, start_time_col])
    end_date <- as.character(input_matrix[i, end_date_col])
    end_time <- as.character(input_matrix[i, end_time_col])
    location <- as.character(input_matrix[i, location_col])
    model <- as.character(input_matrix[i, model_col])
    software <- as.character(input_matrix[i, software_col])
    battery <- as.character(input_matrix[i, battery_col])
    rider_type <- as.character(input_matrix[i, rider_type_col])
    drive_cycle <- as.character(input_matrix[i, drive_cycle_col])
    shift_date <- as.character(input_matrix[i, shift_date_col])
    sql_query <- paste0("
                  with bcm as
                    (
                    select
                    vehicle_identification_number as bcm_vehicle_identification_number,scooter_state as bcm_scooter_state,vehicle_range_normal,vehicle_range_sport,vehicle_range_hyper,vehicle_mode_level1,vehicle_mode_level2,vehicle_mode_level3,vehicle_mode_level4,remaining_range,overall_pack_soc,vehicle_odo,dcdc_conv_output_voltage,charger_mode,charging_current_avl_range,charger_mode_request,trunk_status,generic_vcudata,vehicle_range_eco,vehicle_software_version as bcm_vehicle_software_version,vehicle_odo_in_meters,regen_availability,available_driving_modes,regen_status,som_module_temp_sense_input_adc1,mcu_section_temp_sense,vcu_temperature,vcu_som_temperature,
                    from_unixtime(timestamp/1000) + interval '330' minute as ts_bcm
                    from telemetrics_v2.dp_hmi_quectel_bcm_data_packet_v2
                    WHERE dt >= '", r_date, "' AND dt <= '", end_date, "'
                    AND from_unixtime(timestamp/1000) + interval '330' minute BETWEEN CAST('", start_date, " ", start_time, "' AS TIMESTAMP) AND CAST('", end_date, " ", end_time, "' AS TIMESTAMP)
                    AND vehicle_identification_number = '", vehicle_identification_number, "'
                    ),
                    bms as
                    (
                    select
                    vehicle_identification_number as bms_vehicle_identification_number,scooter_state as bms_scooter_state,kwhr_charged_discharged_mode,total_regen_amhr,amhr_accumulated_current_cycle,balancing_temp_pdu,battery_current,battery_pack_effective_temp,battery_pack_temp1,battery_pack_temp2,battery_pack_temp3,battery_pack_temp4,battery_pack_temp5,battery_pack_temp6,bms_dcdcoutput_voltage,bus_voltage,cell_voltage_min,cell_voltage_max,charge_amhr_bms00,charge_mode_mbms,discharge_amphr_bms00,measured_cell1_voltage,measured_cell2_voltage,measured_cell3_voltage,measured_cell4_voltage,measured_cell5_voltage,measured_cell6_voltage,measured_cell7_voltage,measured_cell8_voltage,measured_cell9_voltage,measured_cell10_voltage,measured_cell11_voltage,measured_cell12_voltage,measured_cell13_voltage,measured_cell14_voltage,balancing_started_due_deviation_count,pack_voltage,pdu_temp1,pdu_temp_afe,pack_soc,pack_soh,time_charge80_fc,time_charge_optimum_fc,time_charge80_sc,time_charge_full_sc,total_balancing_duration,total_charge_time,generic_bms_data,balancing_temp,overall_charge_voltage_limit,display_soc,bms00_cell_balancing_temperature,charge_current_limit00,discharge_current_limit00,charge_voltage_limit00,bms00_pdu_delta_temperature,vehicle_software_version as bms_vehicle_software_version,overall_discharge_current_limit,overall_charge_current_limit,charging_voltage_available_range,charging_current_availablerange,charging_rate,soc_val_from_min_ocv,soc_debug1,factored_discharge_amphr,factored_charge_amphr,serial_number,evse_identification_low_byte,evse_identification_high_byte,discharge_mode,
                    from_unixtime(timestamp/1000) + interval '330' minute as ts_bms
                    from telemetrics_v2.dp_hmi_quectel_bms_data_packet_v2
                    WHERE dt >= '", r_date, "' AND dt <= '", end_date, "'
                    AND from_unixtime(timestamp/1000) + interval '330' minute BETWEEN CAST('", start_date, " ", start_time, "' AS TIMESTAMP) AND CAST('", end_date, " ", end_time, "' AS TIMESTAMP)
                    AND vehicle_identification_number = '", vehicle_identification_number, "'
                    ),
                    mcu as
                    (
                    select
                    vehicle_identification_number as mcu_vehicle_identification_number,scooter_state as mcu_scooter_state,mcu_temperature,motor_temperature,motor_rpm,throttle_value1,throttle_value2,mcu_voltage,motor_current,motor_torque,vehicle_speed_kmph,distance_covered_for_current_trip,mcu_dc_current,bus_voltage_mcu,vehicle_software_version as mcu_vehicle_software_version,dccurrent_mcu,from_unixtime(timestamp/1000) +
                    interval '330' minute as ts_mcu
                    from telemetrics_v2.dp_hmi_quectel_mcu_data_packet_v2
                    WHERE dt >= '", r_date, "' AND dt <= '", end_date, "'
                    AND from_unixtime(timestamp/1000) + interval '330' minute BETWEEN CAST('", start_date, " ", start_time, "' AS TIMESTAMP) AND CAST('", end_date, " ", end_time, "' AS TIMESTAMP)
                    AND vehicle_identification_number = '", vehicle_identification_number, "'
                    ),
                  
                    hpe as
                    (
                    select
                    vehicle_identification_number as hpe_vehicle_identification_number,
                    event_data[1][1] as event_name,
                    event_data[1][2] as event_value,
                    event_type,
                  
                    scooter_state as hpe_scooter_state,
                    from_unixtime(TIMESTAMP/1000) + interval '330' MINUTE AS ts_hpe
                    from telemetrics_v2.dp_hmi_quectel_event_hpe_packet_v2  
                    WHERE dt >= '", r_date, "' AND dt <= '", end_date, "'
                    AND from_unixtime(timestamp/1000) + interval '330' minute BETWEEN CAST('", start_date, " ", start_time, "' AS TIMESTAMP) AND CAST('", end_date, " ", end_time, "' AS TIMESTAMP)
                    AND vehicle_identification_number = '", vehicle_identification_number, "'
                    ),

                    lpe as
                    (
                    select
                    vehicle_identification_number as lpe_vehicle_identification_number,
                    event_data[1][1] as lpe_event_name,
                    event_data[1][2] as lpe_event_value,
                    event_type as lpe_event_type,

                    scooter_state as lpe_scooter_state,
                    from_unixtime(TIMESTAMP/1000) + interval '330' MINUTE AS ts_lpe
                    from telemetrics_v2.dp_hmi_quectel_event_lpe_packet_v2_lpen  
                    WHERE dt >= '", r_date, "' AND dt <= '", end_date, "'
                    AND from_unixtime(timestamp/1000) + interval '330' minute BETWEEN CAST('", start_date, " ", start_time, "' AS TIMESTAMP) AND CAST('", end_date, " ", end_time, "' AS TIMESTAMP)
                    AND vehicle_identification_number = '", vehicle_identification_number, "'
                    )
                    
                    select * from
                    (select
                    *,
                    coalesce(bcm.ts_bcm, bms.ts_bms, mcu.ts_mcu, hpe.ts_hpe, lpe.ts_lpe) as final_time
                  
                    from bcm
                    full outer join bms on bcm.bcm_vehicle_identification_number = bms.bms_vehicle_identification_number and bcm.ts_bcm = bms.ts_bms
                    full outer join mcu on bms.bms_vehicle_identification_number = mcu.mcu_vehicle_identification_number and bms.ts_bms = mcu.ts_mcu
                    full outer join hpe on bms.bms_vehicle_identification_number = hpe.hpe_vehicle_identification_number and bms.ts_bms=hpe.ts_hpe
                    full outer join lpe on bms.bms_vehicle_identification_number = lpe.lpe_vehicle_identification_number and bms.ts_bms=lpe.ts_lpe)
                    order by final_time
                  ")
    
    charging_data_sql <- paste0("

    ")

    tryCatch({
      folder_list <- drive_ls(path = cloud_path)
      shift_date_folder <- folder_list[folder_list$name == shift_date,]
      # check if shift_date_folder$id is empty
      if (length(shift_date_folder$id)==0) {
        # create shift_date_folder
        shift_date_folder_id <- folder_creator(path = cloud_folder_id, name=shift_date)
      } else if(length(shift_date_folder$id)==1){
        shift_date_folder_id <- as_id(shift_date_folder)
      } else if(length(shift_date_folder$id)>1){
        shift_date_folder_id <- folder_merger(as_id(shift_date_folder))
      }
    }, error = function(err) {
      cat("Drive ls failed:", err$message, "\n")
      drive_operations_successful <- FALSE
    })
    if (!drive_operations_successful) {
      cat("Skipping Query", i, "due to drive operations failure.\n")
      next  # Skip the rest of the iteration
    }
    # Skip the iteration if vehicle_identification_number is empty
    if (is.na(vehicle_identification_number) || vehicle_identification_number == "") {
      cat("Skipping Query", i, "as vehicle_identification_number is empty.\n")
      next
    }
    # Generate the output file name based on the values in the current row
    output_file_name <- paste0(vehicle_identification_number, "_", start_date, "_", end_date, "_", start_time, "_", end_time, "_", location, "_", model, "_", software, "_", battery, "_", rider_type, "_", drive_cycle, ".csv")
    # Print the vehicle identification number for debugging purposes
    cat("Processing Query", i, "- Vehicle ID:", vehicle_identification_number, "\n")
    # Set the output file path
    output_file_path <- file.path(output_directory, output_file_name)
    file_names <- list()
    tryCatch({
      # list file names in drive shift_date_folder_id
      file_names <- drive_ls(path = shift_date_folder_id)$name
    }, error = function(err) {
      cat("Drive list failed:", err$message, "\n")
    })
    # check if output_file_name is already present in shift_date_folder_id
    if(output_file_name %in% file_names) {
      cat("Skipping Query", i, "as output_file_name is already present in shift_date_folder_id.\n")
      next
    }else{
      if (!(output_file_name %in% list.files(output_directory))) {
        # Execute the query within a future with a timeout
        tryCatch({
          tr_data_today <- dbGetQuery(conn1, sql_query)
        }, error = function(err) {
          cat("Query failed:", err$message, "\n")
          sql_operations_successful <- FALSE
        })
        if (!sql_operations_successful) {
          cat("Skipping Query", i, "due to SQL operations failure.\n")
          next  # Skip the rest of the iteration
        }
        # tr_data_today <- dbGetQuery(conn1, sql_query)
        tr_data_today <- as.data.table(tr_data_today)
        
        # Write the data table to a local CSV file
        cat("Writing data to local file...", "\n")
        fwrite(tr_data_today, file = output_file_path)
        cat("Data written to local file:", output_file_path, "\n")
        
        # input_data[i, "Processed"] <- "yes"
        # # write_sheet(input_data, sheet_id, sheet = worksheet_name)
        # retry_gs_operation(function() {
        #   googlesheets4::write_sheet(input_data, sheet_id, sheet = worksheet_name)
        # })
      }
      cat("Uploading to drive...", "\n")
      # Upload to Google Drive
      tryCatch({
        cat("Trying ...\n")
        drive_upload(media = output_file_path, path = shift_date_folder_id, name = output_file_name, type = "text/csv", overwrite = TRUE)
        cat("Uploaded to drive:", output_file_name, "\n")
      }, error = function(err) {
        cat("Drive upload failed:", err$message, "\n")
      })
      cat("Successfully updated the input sheet.", "\n")
    }
  }
  # within tryCatch(), open gatherer, sheet_name="ONLINE STATUS", column 'J' has user_name, place current time(+5:30 gmt) in the corresponding row in column K
  tryCatch({
    # Read gatherer sheet, specifying the range as A:B
    gatherer_sheet <- read_sheet(gatherer_sheet_id, range = "ONLINE STATUS!A:B")
    # Get the row number where user_name matches
    user_name_row <- which(gatherer_sheet[, 1] == user_name)[1]
    # Get the current time and format it as "hh:mm AM/PM"
    # current_time <- format(Sys.time() + 19800, format = "%I:%M %p")
    current_time <- format(Sys.time() + 19800, format = "%d-%m-%Y %I:%M %p")
    # Place the formatted current time in the corresponding row in column K
    gatherer_sheet[user_name_row, 2] <- current_time
    # Write the updated gatherer sheet back
    write_sheet(gatherer_sheet, gatherer_sheet_id, sheet = "ONLINE STATUS")
  }, error = function(err) {
    cat("Gatherer sheet update failed:", err$message, "\n")
  })
  # end tryCatch()
}else{
  cat("No data to process.", "\n")
}