#!/usr/bin/env python3
"""
Client Script Generator
This script reads clients.txt and generates individual downloader_client_[username].r files
with proper email and worksheet name replacements
"""

import re
import sys
from pathlib import Path

def main():
    print("=" * 60)
    print("CLIENT SCRIPT GENERATOR")
    print("=" * 60)
    print()
    
    # Configuration
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    clients_file = script_dir / "clients.txt"
    template_file = project_root / "downloader_client.r"
    output_dir = script_dir / "generated_scripts"
    
    # Create output directory if it doesn't exist
    output_dir.mkdir(exist_ok=True)
    
    # Read the list of clients
    if not clients_file.exists():
        print(f"❌ Error: clients.txt file not found at {clients_file}")
        sys.exit(1)
    
    with open(clients_file, 'r') as f:
        clients = [line.strip() for line in f if line.strip()]
    
    print(f"Found {len(clients)} clients in clients.txt:")
    for client in clients:
        print(f"  - {client}")
    print()
    
    # Read the template file
    if not template_file.exists():
        print(f"❌ Error: downloader_client.r template file not found at {template_file}")
        sys.exit(1)
    
    with open(template_file, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # Generate scripts for each client
    generated_files = []
    
    for username in clients:
        if not username:
            continue
            
        print(f"🔧 Generating script for: {username}")
        
        # Create email address and worksheet name
        email = f"{username}@olaelectric.com"
        # Handle both cases: with and without period in username
        name_parts = username.split('.')
        first_name = name_parts[0] if name_parts else username
        worksheet_name = f"{first_name.upper()}_CLOUD"
        
        print(f"   📧 Email: {email}")
        print(f"   📊 Worksheet: {worksheet_name}")
        
        # Replace placeholders in template
        modified_content = template_content
        
        # Replace email addresses (both drive_auth patterns)
        modified_content = re.sub(
            r'drive_auth\(email = "[^"]*"\)',
            f'drive_auth(email = "{email}")',
            modified_content
        )
        
        modified_content = re.sub(
            r'drive_conn <- drive_auth\(email = "[^"]*"\)',
            f'drive_conn <- drive_auth(email = "{email}")',
            modified_content
        )
        
        # Replace worksheet name
        modified_content = re.sub(
            r'worksheet_name <- "[^"]*"',
            f'worksheet_name <- "{worksheet_name}"',
            modified_content
        )
        
        # Create output filename and path
        output_filename = f"downloader_client_{username}.r"
        output_path = output_dir / output_filename
        
        # Write the generated script
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"   ✅ Generated: {output_path}")
        generated_files.append((output_path, username, email, worksheet_name))
        print()
    
    # Validation
    print("🔍 VALIDATING GENERATED SCRIPTS...")
    validation_passed = True
    
    for file_path, username, expected_email, expected_worksheet in generated_files:
        if not file_path.exists():
            print(f"❌ File not found: {file_path}")
            validation_passed = False
            continue
        
        # Read and validate content
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if replacements were made correctly
        email_found = expected_email in content
        worksheet_found = expected_worksheet in content
        
        if not email_found:
            print(f"❌ Email not found in {file_path.name} - Expected: {expected_email}")
            validation_passed = False
        
        if not worksheet_found:
            print(f"❌ Worksheet not found in {file_path.name} - Expected: {expected_worksheet}")
            validation_passed = False
        
        if email_found and worksheet_found:
            print(f"✅ Validated: {file_path.name}")
    
    print()
    
    # Summary
    print("=" * 60)
    print("CLIENT SCRIPT GENERATION COMPLETE")
    print("=" * 60)
    
    if validation_passed:
        print("✅ All validations passed!")
    else:
        print("❌ Some validations failed. Please check the generated scripts.")
    
    print(f"\nGenerated {len(generated_files)} client scripts:\n")
    
    for file_path, username, email, worksheet in generated_files:
        print(f"📄 {file_path.name}")
        print(f"   👤 User: {username}")
        print(f"   📧 Email: {email}")
        print(f"   📊 Worksheet: {worksheet}")
        print()
    
    print(f"📁 All scripts saved in: {output_dir}")
    print("\n🔧 Usage Instructions:")
    print("1. Copy the appropriate script to each user's environment")
    print("2. Ensure each user has access to their worksheet (e.g., GARIMA_CLOUD)")
    print("3. Run the master script first to create date folders")
    print("4. Then run individual client scripts")
    
    if validation_passed:
        print("\n✅ Script generation completed successfully!")
    else:
        print("\n⚠️  Script generation completed with warnings. Please review.")

if __name__ == "__main__":
    main()
