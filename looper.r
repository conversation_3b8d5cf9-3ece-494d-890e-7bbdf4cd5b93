current_dir <- getwd()
# file_full_path <- paste(current_dir, file_name, sep = "/")
out_path <- paste(current_dir, "output-sql-24h", sep = "/")

# R Script to run new_r_script_gatherer.r every 2 hours in a continuous loop
# Define the time interval in seconds (2 hours = 7200 seconds)
interval_seconds <- 1800

while (TRUE) {
  tryCatch({
    # run function to remove files older than 10 days
    remove_old_files(out_path)
  }, error = function(e) {
    cat("Error occurred:", conditionMessage(e), "\n")
  })
  tryCatch({
    # Run "downloader.R" in the same folder
    source("downloader.R")
    # print status message
    print(paste("R script executed at", Sys.time())) 
  }, error = function(e) {
    # Handle the error, e.g., print an error message
    cat("Error occurred:", conditionMessage(e), "\n")
  })
  # Sleep for the specified interval
  Sys.sleep(interval_seconds)
}

# function to remove CSV files within output_path older than 10 days
remove_old_files <- function(output_path) {
  # get list of files in output_path
  files <- list.files(output_path, full.names = TRUE)
  # get file creation time
  file_creation_time <- file.info(files)$ctime
  # get current time
  current_time <- Sys.time()
  # get time difference in days
  time_diff <- as.numeric(difftime(current_time, file_creation_time, units = "days"))
  # get files older than 10 days
  files_to_remove <- files[time_diff > 10]
  # filter only CSV files
  csv_files_to_remove <- grep("\\.csv$", files_to_remove, value = TRUE)
  # remove files
  file.remove(csv_files_to_remove)
}