# Charging Data Gather Project

This project contains scripts for gathering and processing charging data.

## Components:

### R Scripts:
-   **`downloader_client.r`**: An R script designed to connect to Presto, fetch data based on parameters from a Google Sheet, and upload results to Google Drive. It utilizes OAuth 2.0 client credentials for Google API authentication.
-   **`downloader_master.r`**: (Purpose to be added - likely orchestrates or manages `downloader_client.r` instances).
-   **`looper.r`**: (Purpose to be added - likely involves looping or scheduling script execution).

### Google Apps Scripts (`app_scripts/`):
-   **`gatherer.js`**: Contains Google Apps Script functions to manipulate data within Google Sheets. This includes:
    -   Aggregating data from various log sheets into a central `ALL` sheet.
    -   A function `syncVinIccidToAllSheet` to populate the `ALL` sheet based on values from a `VIN_ICCID` sheet, performing necessary transformations.
-   **`constants.js`**: Stores constants (like Sheet IDs and names) used by the Apps Scripts.
-   **`all_data.js`**: (Purpose to be added).

### Authentication:
-   The R scripts use an OAuth 2.0 Client ID (stored in `auth/auth.json`) for authenticating with Google services (Sheets and Drive).

### Plans & Tasks (`readme/tasks/`):
-   Contains markdown files detailing plans for specific development tasks, such as `plan_populate_all_sheet_from_vin_iccid.md`.

## Setup & Usage:

(Details to be added as the project evolves)

-   **R Environment**: Ensure necessary R libraries are installed (e.g., `googlesheets4`, `googledrive`, `DBI`, `RPresto`, `gargle`, etc.).
-   **Google Cloud Project**: OAuth credentials (`auth/auth.json`) should be configured correctly.
-   **Google Apps Script**: Deploy scripts from the `app_scripts` directory to your Google Sheets environment.