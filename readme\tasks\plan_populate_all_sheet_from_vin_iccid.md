# Plan to Populate `ALL` Sheet from `VIN_ICCID` Data

**1. Objective:**
Create a Google Apps Script function to transfer and transform data from the `VIN_ICCID` sheet (source) to the `ALL` sheet (destination).
*   **Source:** Spreadsheet ID `ACTIVE_VIN_LIST_SHEET` ("1bEA2XtFGZEXgCBjkDqWgjBohCat02AAj6rCq-3QMuGI"), Sheet Name `VIN_ICCID_SHEET_NAME` ("VIN_ICCID").
*   **Destination:** Spreadsheet ID `gathererSheetId` ("1gHlGLQbQ27mymh3nXVybpHwXxrnm2Lb4z8_vT-VEP9A"), Sheet Name "ALL".

**2. Constants to be Used (from `app_scripts/constants.js`):**
*   `SOURCE_SPREADSHEET_ID`: `ACTIVE_VIN_LIST_SHEET`
*   `SOURCE_SHEET_NAME`: `VIN_ICCID_SHEET_NAME`
*   `DEST_SPREADSHEET_ID`: `gathererSheetId`
*   `DEST_SHEET_NAME`: "ALL" (This will be defined in the script, as it's not in `app_scripts/constants.js`)
*   `PLACEHOLDER_VALUE`: "XX" (This will be defined in the script)

**3. Function Definition:**
   A new function will be created, for example, `syncVinIccidToAllSheet()`.

**4. Core Logic Steps:**

   *   **Step 4.1: Access Spreadsheets and Sheets**
        *   Open the source spreadsheet using `SpreadsheetApp.openById(SOURCE_SPREADSHEET_ID)`.
        *   Get the source sheet (`VIN_ICCID`) by name: `sourceSpreadsheet.getSheetByName(SOURCE_SHEET_NAME)`.
        *   Open the destination spreadsheet using `SpreadsheetApp.openById(DEST_SPREADSHEET_ID)`.
        *   Get the destination sheet (`ALL`) by name: `destSpreadsheet.getSheetByName(DEST_SHEET_NAME)`.
        *   **If the `ALL` sheet does not exist, create it:** `destSpreadsheet.insertSheet(DEST_SHEET_NAME)`.

   *   **Step 4.2: Read Headers**
        *   Read headers from the `ALL` sheet (e.g., `CONFIG`, `LOCATION`, `VIN`, etc.). If the sheet was just created, define the standard `ALL` sheet headers.
        *   Read headers from the `VIN_ICCID` sheet (e.g., `S.No`, `VIN`, `ICCID`, `Model`, `Variant`, etc.) to dynamically find column indices.

   *   **Step 4.3: Read Source Data**
        *   Read all data rows (excluding the header row) from the `VIN_ICCID` sheet.

   *   **Step 4.4: Data Transformation and Mapping**
        *   Initialize an empty array, `allSheetData`, to hold the rows of transformed data.
        *   Create a mapping for `VIN_ICCID` header names to their column indices.
        *   Iterate through each row read from the `VIN_ICCID` sheet. For each source row:
            *   Create a new target row array corresponding to the `ALL` sheet headers.
            *   Populate the target row based on the following mapping:
                *   `ALL.CONFIG`: Concatenate `VIN_ICCID.Model` + "-" + `VIN_ICCID.Variant`. Handle potential empty values for Model or Variant.
                *   `ALL.LOCATION`: Map from `VIN_ICCID.Location`.
                *   `ALL.VIN`: Map from `VIN_ICCID.VIN`.
                *   `ALL.BATTERY`: Map from `VIN_ICCID.Battery Type`.
                *   `ALL.SOFTWARE`: Map from `VIN_ICCID.S/W`.
                *   `ALL.SHIFT_DATE`: Fill with `PLACEHOLDER_VALUE` ("XX").
                *   `ALL.START_DATE`: Fill with `PLACEHOLDER_VALUE` ("XX").
                *   `ALL.END_DATE`: Fill with `PLACEHOLDER_VALUE` ("XX").
                *   `ALL.START_TIME`: Fill with `PLACEHOLDER_VALUE` ("XX").
                *   `ALL.END_TIME`: Fill with `PLACEHOLDER_VALUE` ("XX").
                *   `ALL.RIDER_TYPE`: Fill with `PLACEHOLDER_VALUE` ("XX").
                *   `ALL.DRIVE_CYCLE`: Fill with `PLACEHOLDER_VALUE` ("XX").
                *   `ALL.DISTANCE`: Fill with `PLACEHOLDER_VALUE` ("XX").
                *   `ALL.DATA_LINK`: Fill with `PLACEHOLDER_VALUE` ("XX").
            *   If a source value is empty/undefined for a mapped field, use `PLACEHOLDER_VALUE` in the target row.
            *   Add the completed target row to the `allSheetData` array.

   *   **Step 4.5: Write Data to `ALL` Sheet**
        *   Clear all existing content and formatting from the `ALL` sheet (using `allSheet.clear()`).
        *   Write the headers to the first row of the `ALL` sheet. (Use predefined `ALL` sheet headers if the sheet was newly created or to ensure consistency).
        *   Apply formatting to the header row (bold, center alignment, background color `#D9D9D9`), similar to the example in [`app_scripts/gatherer.js`](app_scripts/gatherer.js:398-442).
        *   If `allSheetData` contains data, write it to the `ALL` sheet starting from the second row. If `VIN_ICCID` was empty, `allSheetData` will be empty, and only headers will be written to `ALL`.

**5. Error Handling and Logging:**
    *   Implement `try-catch` blocks for robust error handling.
    *   Use `Logger.log()` for status messages and debugging information (e.g., number of rows processed, whether `ALL` sheet was created).

**6. Visual Workflow (Updated based on clarifications):**
```mermaid
graph TD
    A[Start: syncVinIccidToAllSheet] --> B{Access Spreadsheets & Sheets};
    B -- Check/Create ALL Sheet --> B1[ALL Sheet Ready]
    B1 -- Source: VIN_ICCID Sheet --> C{Read VIN_ICCID Headers & Data};
    B1 -- Dest: ALL Sheet --> D{Read/Define ALL Sheet Headers};
    C --> E{Iterate VIN_ICCID Rows};
    D --> E;
    E --> F{Transform Data & Map to ALL Format};
    F -- Create targetRow --> G{Collect Transformed Rows};
    G --> H{Write to ALL Sheet};
    H -- 1. Clear ALL Sheet --> I[2. Write Headers to ALL & Format];
    I -- 3. Write Transformed Data --> J[End];

    subgraph Input Constants (from app_scripts/constants.js)
        direction LR
        const1[ACTIVE_VIN_LIST_SHEET]
        const2[VIN_ICCID_SHEET_NAME]
        const3[gathererSheetId]
    end
    A --> Input_Constants

    subgraph Transformation Rules (Updated based on clarifications)
        direction TB
        mapRule1[CONFIG = VIN_ICCID.Model + '-' + VIN_ICCID.Variant]
        mapRule2[VIN = VIN_ICCID.VIN]
        mapRule3[LOCATION = VIN_ICCID.Location]
        mapRule4[BATTERY = VIN_ICCID.Battery Type]
        mapRule5[SOFTWARE = VIN_ICCID.S/W]
        mapRule6[DISTANCE = "XX"]
        mapRule7[DATA_LINK = "XX"]
        mapRule8[Other ALL Columns = "XX"]
    end
    F --> Transformation_Rules

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style J fill:#f9f,stroke:#333,stroke-width:2px