// Google App Script to gather specific column data from source sheet to destination sheet
// Sheet Ids
const prodLogSheetId = "1wPIsn4Ggpfya6zvwF64iJRfo5-0GfgYpIew2YeUKRto";
const s1AGen2LogSheetId = "1G7kX96c7ItJ8Y89ZGsJUPOdtNu95uY75RQarXHLG4NE";
const moveOs4LogSheetId = "18Zuvs0wUxTfarLZrGHH5GH2LNgoeQuH1Hjao82LzdSA";
const s1XPlusSheetId = "1DANzkqr-Al-uAfPPRXGrk3OHwHMbSSvHibf_HYCKq-4"


// Sheet Names
const destSheetNameAll = "ALL";
const sheetIdDict = {
    "OTHERS":s1AGen2LogSheetId,
    "PROD":prodLogSheetId,
    "MOVE_OS_4":moveOs4LogSheetId,
    "S1X_PLUS":s1XPlusSheetId,
}

const mapperObject = {
    'CONFIG': 'VEHICLE_CONFIG',
    'LOCATION': 'LOCATION',
    'VIN': 'VIN_NO',
    'BATTERY': 'BATTERY_DETAILS',
    'SOFTWARE': 'SOFTWARE',
    'SHIFT_DATE': 'SHIFT_DATE',
    'START_DATE': 'START_DATE',
    'END_DATE': 'END_DATE',
    'START_TIME': 'START_TIME',
    'END_TIME': 'END_TIME',
    'RIDER_TYPE': 'RIDER_TYPE',
    'DRIVE_CYCLE': 'DRIVE_CYCLE_ROUTE',
    'DISTANCE': 'TOTAL_KM',
    'DATA_LINK': 'RAW_DATA_LINK'
}

const sheetNamesOthers = ['RMZ S1_Air Log_Sheet',
                          'RMZ Gen_2 Log_Sheet',
                          'Ooty S1_Air Log_Sheet',
                          'Ooty Gen_2 Log_Sheet',
                          'Chennai S1_Air Log_Sheet',
                          'Chennai Gen_2 Log_Sheet',
                          'Jaisalmer S1_Air Log_Sheet',
                          'Monsoon S1_Air Log_Sheet',
                          'Monsoon Gen_2 Log_Sheet',
                          'Future_Factory Gen_2 Log_Sheet',
                          'Future_Factory S1_Air Log_Sheet',
                          'Yelagiri S1_AIR GEN_2 Log_Sheet',
                          'Leh Ladakh_S1_Air Log_Sheet',
                          'Leh Ladakh_Gen_2.0 Log_Sheet',
                          'FF-S1 Air PV Log Sheet',
                        ]

const sheetNamesProd = ['RMZ Running Log',
                        'FF_New S/W 3.0.3.gen2.0729 Log',
                        'BLR_New S/W 3.0.3.gen2.0729 Log',
                        'PV Vehicles_Chennai location',
                        ]

const sheetNamesMoveOs4 = ['Running Log',
                        ]

const sheetNamesS1XPlus = ['RMZ S1 X+',
                           'S1 X+ and S1 Air PV B2B_RMZ',
                           'Log Sheet_FF',
                           'Log Sheet FF - Phase 2'
                    ]

const locationObject = {
    "RMZ S1_Air Log_Sheet": "RMZ",
    "RMZ Gen_2 Log_Sheet": "RMZ",
    "Ooty S1_Air Log_Sheet": "OOTY",
    "Ooty Gen_2 Log_Sheet": "OOTY",
    "Chennai S1_Air Log_Sheet": "CHENNAI",
    "Chennai Gen_2 Log_Sheet": "CHENNAI",
    "Jaisalmer S1_Air Log_Sheet": "JAISALMER",
    "Monsoon S1_Air Log_Sheet": "MONSOON",
    "Monsoon Gen_2 Log_Sheet": "MONSOON",
    "Future_Factory Gen_2 Log_Sheet": "FF",
    "Future_Factory S1_Air Log_Sheet": "FF",
    "Yelagiri S1_AIR GEN_2 Log_Sheet": "YELAGIRI",
    'RMZ Running Log': "RMZ PROD",
    'FF_New S/W 3.0.3.gen2.0729 Log': 'FF PROD',
    'BLR_New S/W 3.0.3.gen2.0729 Log': "BLR PROD",
    'PV Vehicles_Chennai location': 'CHENNAI PROD',
    'Running Log': 'FF',
    'RMZ S1 X+': 'RMZ',
    'S1 X+ and S1 Air PV B2B_RMZ':'RMZ',
    'Log Sheet_FF':'FF',
    'FF-S1 Air PV Log Sheet':'FF PROD',
    'Leh Ladakh_Gen_2.0 Log_Sheet':'LEH',
    'Leh Ladakh_S1_Air Log_Sheet': 'LEH'
};