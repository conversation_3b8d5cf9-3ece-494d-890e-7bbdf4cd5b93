/**
 * Populates the 'ALL' sheet in the destination spreadsheet (gathererSheetId)
 * with data from the 'VIN_ICCID' sheet in the source spreadsheet (ACTIVE_VIN_LIST_SHEET).
 * It transforms and maps data according to the defined plan.
 */
function syncVinIccidToAllSheet() {
  const PLACEHOLDER_VALUE = "XX";
  // Headers for the "ALL" sheet as per the plan and sample
  const ALL_SHEET_HEADERS = ["CONFIG", "LOCATION", "VIN", "BATTERY", "SOFTWARE", "SHIFT_DATE", "START_DATE", "END_DATE", "START_TIME", "END_TIME", "RIDER_TYPE", "DRIVE_CYCLE", "DISTANC<PERSON>", "DATA_LINK"];

  // Constants ACTIVE_VIN_LIST_SHEET and VIN_ICCID_SHEET_NAME are expected to be globally available from app_scripts/constants.js
  // Global 'destSpreadsheet' and 'destSheetNameAll' from app_scripts/gatherer.js are used.

  try {
    Logger.log("Starting syncVinIccidToAllSheet function...");

    // Step 4.1: Access Spreadsheets and Sheets
    const sourceSpreadsheet = SpreadsheetApp.openById(ACTIVE_VIN_LIST_SHEET);
    const sourceSheet = sourceSpreadsheet.getSheetByName(VIN_ICCID_SHEET_NAME);

    if (!sourceSheet) {
        Logger.log(`Source sheet '${VIN_ICCID_SHEET_NAME}' not found in spreadsheet ID '${ACTIVE_VIN_LIST_SHEET}'. Aborting.`);
        return;
    }
    Logger.log(`Accessed source sheet: '${sourceSheet.getName()}'.`);

    var destinationSheet = destSpreadsheet.getSheetByName(destSheetNameAll);

    if (!destinationSheet) {
      destinationSheet = destSpreadsheet.insertSheet(destSheetNameAll);
      Logger.log(`Sheet '${destSheetNameAll}' created in spreadsheet '${destSpreadsheet.getName()}'.`);
    } else {
      Logger.log(`Sheet '${destSheetNameAll}' found in spreadsheet '${destSpreadsheet.getName()}'.`);
    }

    // Step 4.2 & 4.5 (part 1: Headers): Clear destination and set headers
    destinationSheet.clear();
    headerSetterFormatter(destinationSheet, ALL_SHEET_HEADERS); // Use existing helper
    Logger.log(`Sheet '${destSheetNameAll}' cleared and headers set.`);
    
    // Handle empty or header-only source sheet
    if (sourceSheet.getLastRow() <= 1) {
        Logger.log(`Source sheet '${VIN_ICCID_SHEET_NAME}' has no data rows (or only headers). Operation complete with empty '${destSheetNameAll}' sheet (headers only).`);
        return;
    }

    const sourceHeaders = sourceSheet.getRange(1, 1, 1, sourceSheet.getLastColumn()).getValues()[0];
    const sourceHeaderIndexMap = {};
    sourceHeaders.forEach((header, index) => {
      sourceHeaderIndexMap[String(header).trim()] = index; // Ensure header is string and trimmed
    });
    Logger.log(`Source headers mapped: ${JSON.stringify(sourceHeaderIndexMap)}`);

    // Step 4.3: Read Source Data
    const sourceData = sourceSheet.getRange(2, 1, sourceSheet.getLastRow() - 1, sourceSheet.getLastColumn()).getValues();
    Logger.log(`Read ${sourceData.length} data rows from source sheet '${VIN_ICCID_SHEET_NAME}'.`);

    // Step 4.4: Data Transformation and Mapping
    const allSheetData = sourceData.map(row => {
      const targetRow = new Array(ALL_SHEET_HEADERS.length).fill(PLACEHOLDER_VALUE); // Initialize with placeholder

      // CONFIG: Model + " " + Variant
      const model = row[sourceHeaderIndexMap['Model']] ? String(row[sourceHeaderIndexMap['Model']]).trim() : "";
      const variant = row[sourceHeaderIndexMap['Variant']] ? String(row[sourceHeaderIndexMap['Variant']]).trim() : "";
      let configValue = PLACEHOLDER_VALUE;
    //   Model and Variant Concat
      if (model && variant) {
        configValue = `${model} ${variant}`;
      } else if (model) {
        configValue = model;
      } else if (variant) {
        configValue = variant; 
      }
      targetRow[ALL_SHEET_HEADERS.indexOf('CONFIG')] = configValue;

      // LOCATION: Location
      const locationVal = row[sourceHeaderIndexMap['Location']];
      targetRow[ALL_SHEET_HEADERS.indexOf('LOCATION')] = locationVal ? String(locationVal).trim() : PLACEHOLDER_VALUE;
      
      // VIN: VIN
      const vinVal = row[sourceHeaderIndexMap['VIN']];
      targetRow[ALL_SHEET_HEADERS.indexOf('VIN')] = vinVal ? String(vinVal).trim() : PLACEHOLDER_VALUE;
      
    // BATTERY: Battery Type + " " + Battery Capacity
    const batteryType = row[sourceHeaderIndexMap['Battery Type']] ? String(row[sourceHeaderIndexMap['Battery Type']]).trim() : "";
    const batteryCapacity = row[sourceHeaderIndexMap['Battery Capacity']] ? String(row[sourceHeaderIndexMap['Battery Capacity']]).trim() : "";
    let batteryValue = PLACEHOLDER_VALUE;
    if (batteryType && batteryCapacity) {
      batteryValue = `${batteryType} ${batteryCapacity}`;
    } else if (batteryType) {
      batteryValue = batteryType;
    } else if (batteryCapacity) {
      batteryValue = batteryCapacity;
    }
    targetRow[ALL_SHEET_HEADERS.indexOf('BATTERY')] = batteryValue;
      // SOFTWARE: S/W
      const swVal = row[sourceHeaderIndexMap['S/W']]; 
      targetRow[ALL_SHEET_HEADERS.indexOf('SOFTWARE')] = swVal ? String(swVal).trim() : PLACEHOLDER_VALUE;
      
      // Other fields are already 'XX' due to initialization.
      return targetRow;
    });

    // Step 4.5 (part 2: Data): Write data
    if (allSheetData.length > 0) {
      destinationSheet.getRange(2, 1, allSheetData.length, allSheetData[0].length).setValues(allSheetData);
      Logger.log(`Wrote ${allSheetData.length} rows of data to sheet '${destSheetNameAll}'.`);
    } else {
      Logger.log(`No data to write to sheet '${destSheetNameAll}' after processing.`);
    }

    Logger.log("syncVinIccidToAllSheet function completed successfully.");

  } catch (e) {
    Logger.log(`Error in syncVinIccidToAllSheet: ${e.toString()}\nStack: ${e.stack}`);
    // Browser.msgBox(`Error in syncVinIccidToAllSheet: ${e.toString()}`); // Optional: show error to user in UI
  }
}