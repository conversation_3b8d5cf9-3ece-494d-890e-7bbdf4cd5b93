# Client Script Generator
# This script reads clients.txt and generates individual downloader_client_[username].r files
# with proper email and worksheet name replacements

library(stringr)

# Configuration
clients_file <- "client_creator/clients.txt"
template_file <- "downloader_client.r"
output_dir <- "client_creator/generated_scripts"

# Create output directory if it doesn't exist
if (!dir.exists(output_dir)) {
  dir.create(output_dir, recursive = TRUE)
}

# Read the list of clients
if (!file.exists(clients_file)) {
  stop("clients.txt file not found in client_creator/ directory")
}

clients <- readLines(clients_file)
clients <- clients[clients != ""]  # Remove empty lines
clients <- trimws(clients)  # Remove whitespace

cat("Found", length(clients), "clients in clients.txt:\n")
for (client in clients) {
  cat("  -", client, "\n")
}

# Read the template file
if (!file.exists(template_file)) {
  stop("downloader_client.r template file not found")
}

template_content <- readLines(template_file)

# Function to generate client script
generate_client_script <- function(username, template_lines) {
  # Create email address
  email <- paste0(username, "@olaelectric.com")
  
  # Create worksheet name (uppercase username + _CLOUD)
  worksheet_name <- paste0(toupper(username), "_CLOUD")
  
  # Create user name (first part before dot, if any)
  user_name_parts <- strsplit(username, "\\.")[[1]]
  user_name <- toupper(user_name_parts[1])
  
  cat("Generating script for:", username, "\n")
  cat("  Email:", email, "\n")
  cat("  Worksheet:", worksheet_name, "\n")
  cat("  User name:", user_name, "\n")
  
  # Replace placeholders in template
  modified_lines <- template_lines
  
  # Replace email addresses (both drive_auth and drive_conn lines)
  modified_lines <- str_replace_all(modified_lines, 
                                   'drive_auth\\(email = "[^"]*"\\)', 
                                   paste0('drive_auth(email = "', email, '")'))
  
  modified_lines <- str_replace_all(modified_lines, 
                                   'drive_conn <- drive_auth\\(email = "[^"]*"\\)', 
                                   paste0('drive_conn <- drive_auth(email = "', email, '")'))
  
  # Replace worksheet name
  modified_lines <- str_replace_all(modified_lines, 
                                   'worksheet_name <- "[^"]*"', 
                                   paste0('worksheet_name <- "', worksheet_name, '"'))
  
  # Replace user name (derived from worksheet_name split)
  # The existing logic should work: user_name <- strsplit(worksheet_name, "_")[[1]][1]
  # This will automatically extract the correct user name from the new worksheet_name
  
  return(modified_lines)
}

# Generate scripts for each client
generated_files <- c()

for (username in clients) {
  if (username == "") next  # Skip empty usernames
  
  # Generate the modified script content
  client_script_content <- generate_client_script(username, template_content)
  
  # Create output filename
  output_filename <- paste0("downloader_client_", username, ".r")
  output_path <- file.path(output_dir, output_filename)
  
  # Write the generated script
  writeLines(client_script_content, output_path)
  
  cat("✅ Generated:", output_path, "\n")
  generated_files <- c(generated_files, output_path)
}

# Validation: Check generated files
cat("\n🔍 VALIDATING GENERATED SCRIPTS...\n")
validation_passed <- TRUE

for (i in 1:length(generated_files)) {
  file_path <- generated_files[i]
  username <- clients[i]

  if (!file.exists(file_path)) {
    cat("❌ File not found:", file_path, "\n")
    validation_passed <- FALSE
    next
  }

  # Read and validate content
  content <- readLines(file_path)
  expected_email <- paste0(username, "@olaelectric.com")
  expected_worksheet <- paste0(toupper(username), "_CLOUD")

  # Check if replacements were made correctly
  email_found <- any(grepl(expected_email, content, fixed = TRUE))
  worksheet_found <- any(grepl(expected_worksheet, content, fixed = TRUE))

  if (!email_found) {
    cat("❌ Email not found in", file_path, "- Expected:", expected_email, "\n")
    validation_passed <- FALSE
  }

  if (!worksheet_found) {
    cat("❌ Worksheet not found in", file_path, "- Expected:", expected_worksheet, "\n")
    validation_passed <- FALSE
  }

  if (email_found && worksheet_found) {
    cat("✅ Validated:", basename(file_path), "\n")
  }
}

# Summary
cat("\n", paste(rep("=", 60), collapse=""), "\n")
cat("CLIENT SCRIPT GENERATION COMPLETE\n")
cat(paste(rep("=", 60), collapse=""), "\n")
if (validation_passed) {
  cat("✅ All validations passed!\n")
} else {
  cat("❌ Some validations failed. Please check the generated scripts.\n")
}

cat("Generated", length(generated_files), "client scripts:\n\n")

for (i in 1:length(generated_files)) {
  file <- generated_files[i]
  username <- clients[i]
  expected_email <- paste0(username, "@olaelectric.com")
  expected_worksheet <- paste0(toupper(username), "_CLOUD")

  cat("📄", basename(file), "\n")
  cat("   👤 User:", username, "\n")
  cat("   📧 Email:", expected_email, "\n")
  cat("   📊 Worksheet:", expected_worksheet, "\n\n")
}

cat("📁 All scripts saved in:", output_dir, "\n")
cat("\n🔧 Usage Instructions:\n")
cat("1. Copy the appropriate script to each user's environment\n")
cat("2. Ensure each user has access to their worksheet (e.g., GARIMA_CLOUD)\n")
cat("3. Run the master script first to create date folders\n")
cat("4. Then run individual client scripts\n")

if (validation_passed) {
  cat("\n✅ Script generation completed successfully!\n")
} else {
  cat("\n⚠️  Script generation completed with warnings. Please review.\n")
}
